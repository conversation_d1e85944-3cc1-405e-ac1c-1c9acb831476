FastPro Extension
=================

Contains the next function

* Bytes2List(cBytes) —> aList // [[R,G,B],...]

* List2Bytes(aList) —> cBytes // "RGBA...."

* updateList(aList,cCommand,cSelection,nPara1,nPara2,[nPara3])

* updateColumn(aList, [cCommand,nPara1,nPara2,[nPara3]],…)

* updateBytesColumn(cBytes, nColumns, nCount, nDiv, [cCommand,nPara1,nPara2,[nPara3]],…) —> cNewBytes 

* addBytesColumn(cBytes, nColumns, nCount) —> cNewBytes 